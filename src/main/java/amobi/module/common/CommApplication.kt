package amobi.module.common

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.interstitial_ad.AdvertsInstanceInter
import amobi.module.common.advertisements.open_ad.AdvertsInstanceOpenAd
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.PrefComm
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import android.app.Activity
import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.RequestConfiguration
import java.util.concurrent.atomic.AtomicBoolean

open class CommApplication :
    Application(),
    Application.ActivityLifecycleCallbacks,
    DefaultLifecycleObserver {
    companion object {
        var lastVersionCode = 0
        var previewContext: Context? = null
        var nullableInstance: CommApplication? = null

        var fullScreenAdInstance: AdvertsInstance? = null

        val appContext: Context
            get() = nullableInstance?.applicationContext ?: previewContext!!

        val startupTimeInSeconds = MixedUtils.currentTimeSeconds()
        var foregroundTimeInSeconds: Long = MixedUtils.currentTimeSeconds()

        private var isAdvertsInitialized = AtomicBoolean(false)

        fun initAdMob(
            context: Context,
            onAdmobInitialized: (() -> Unit)? = null,
        ) {
            if (isAdvertsInitialized.getAndSet(true)) {
                onAdmobInitialized?.invoke()
                return
            }
            if (CommFigs.IS_ADD_TEST_DEVICE) {
                val testDeviceIds: MutableList<String> = java.util.ArrayList()
                testDeviceIds.add(AdvertsConfig.instance.getAdMobDeviceAndroidId(context))
                val config = RequestConfiguration.Builder().setTestDeviceIds(testDeviceIds).build()
                MobileAds.setRequestConfiguration(config)
            } else {
                MobileAds.setRequestConfiguration(
                    RequestConfiguration.Builder().build(),
                )
            }
            MobileAds.initialize(context) {
                onAdmobInitialized?.invoke()
            }
        }

        var isForeground = false

        var isShowingFullscreenAds = false
    }

    override fun onCreate() {
        nullableInstance = this

        super<Application>.onCreate()

        classifyDevice()
    }

    private fun classifyDevice() {
        val actManager = baseContext.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        val memInfo = ActivityManager.MemoryInfo()
        actManager.getMemoryInfo(memInfo)
        val totalMemGigabyte = memInfo.totalMem / (1024.0 * 1024.0 * 1024.0)

        // println("totalMemGigabyte ${totalMemGigabyte}")
        // Set device capability flags based on memory
        when {
            totalMemGigabyte < 2 -> {
                CommFigs.IS_SUPER_WEAK_DEVICE = true
                CommFigs.IS_WEAK_DEVICE = true
                CommFigs.IS_LOWER_6GB_DEVICE = true
            }

            totalMemGigabyte < 3.6 -> {
                CommFigs.IS_SUPER_WEAK_DEVICE = false
                CommFigs.IS_WEAK_DEVICE = true
                CommFigs.IS_LOWER_6GB_DEVICE = true
            }

            totalMemGigabyte < 5.6 -> {
                CommFigs.IS_SUPER_WEAK_DEVICE = false
                CommFigs.IS_WEAK_DEVICE = false
                CommFigs.IS_LOWER_6GB_DEVICE = true
            }

            else -> {
                CommFigs.IS_SUPER_WEAK_DEVICE = false
                CommFigs.IS_WEAK_DEVICE = false
                CommFigs.IS_LOWER_6GB_DEVICE = false
            }
        }
    }

    private var isAppInitialized = AtomicBoolean(false)

    open fun appInitialize(): Boolean {
        if (isAppInitialized.getAndSet(true))
            return false

        registerActivityLifecycleCallbacks(this)
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        FirebaseAssist.instance.setContextFireBase()

        lastVersionCode = PrefAssist.getInt(PrefComm.PREF_LAST_VERSION_CODE, 0)
        val currentVersionCode = MixedUtils.currentVersionCode()
        if (currentVersionCode > 0 && currentVersionCode != lastVersionCode) {
            PrefAssist.setInt(PrefComm.PREF_LAST_VERSION_CODE, currentVersionCode)
        }
        return true
    }

    private var timeSinceOutApp = 0L

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        isForeground = true
        if (timeSinceOutApp > 0) {
            invokeSinceOutApp(System.currentTimeMillis() - timeSinceOutApp)
            timeSinceOutApp = 0
        }
    }

    open fun invokeSinceOutApp(millisSinceOutApp: Long) {
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        isForeground = true
        foregroundTimeInSeconds = MixedUtils.currentTimeSeconds()
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        isForeground = false
        timeSinceOutApp = System.currentTimeMillis()
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        isForeground = false
    }

    override fun onActivityCreated(
        activity: Activity,
        bundle: Bundle?,
    ) {
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
        fullScreenAdInstance?.let { fullScreenAdInstance ->
            if (fullScreenAdInstance is AdvertsInstanceInter)
                fullScreenAdInstance.onActivityResumed()
            else if (fullScreenAdInstance is AdvertsInstanceOpenAd)
                fullScreenAdInstance.onActivityResumed()
        }
    }

    override fun onActivityPaused(activity: Activity) {
        fullScreenAdInstance?.let { fullScreenAdInstance ->
            if (fullScreenAdInstance is AdvertsInstanceInter)
                fullScreenAdInstance.onActivityPaused()
            else if (fullScreenAdInstance is AdvertsInstanceOpenAd)
                fullScreenAdInstance.onActivityPaused()
        }
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(
        activity: Activity,
        bundle: Bundle,
    ) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }
}
