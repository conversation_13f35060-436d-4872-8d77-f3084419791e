{"formatVersion": 1, "database": {"version": 2, "identityHash": "00a05670f2867d0f9ff2dccefaee3072", "entities": [{"tableName": "chat_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chatListId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `historyId` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, `content` TEXT NOT NULL, `isHuman` INTEGER NOT NULL, `isError` INTEGER NOT NULL DEFAULT 0, `imageData` BLOB, `botName` TEXT, FOREIGN KEY(`historyId`) REFERENCES `history_table`(`historyId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "chatListId", "columnName": "chatListId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "historyId", "columnName": "historyId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isHuman", "columnName": "isHuman", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isError", "columnName": "isError", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "imageData", "columnName": "imageData", "affinity": "BLOB", "notNull": false}, {"fieldPath": "botName", "columnName": "botName", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["chatListId"]}, "indices": [], "foreignKeys": [{"table": "history_table", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["historyId"], "referencedColumns": ["historyId"]}]}, {"tableName": "history_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`historyId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `timestamp` INTEGER NOT NULL, `historyName` TEXT NOT NULL, `isFavorite` INTEGER NOT NULL, `content` TEXT NOT NULL, `imageData` BLOB, `questionMode` TEXT NOT NULL DEFAULT '', `modelAiChat` TEXT NOT NULL DEFAULT '')", "fields": [{"fieldPath": "historyId", "columnName": "historyId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "historyName", "columnName": "historyName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "isFavorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "imageData", "columnName": "imageData", "affinity": "BLOB", "notNull": false}, {"fieldPath": "questionMode", "columnName": "questionMode", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "modelAiChat", "columnName": "modelAiChat", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}], "primaryKey": {"autoGenerate": true, "columnNames": ["historyId"]}, "indices": [], "foreignKeys": []}, {"tableName": "coin_history_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT NOT NULL, `amount` INTEGER NOT NULL, `date` INTEGER NOT NULL, `description` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "amount", "columnName": "amount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '00a05670f2867d0f9ff2dccefaee3072')"]}}