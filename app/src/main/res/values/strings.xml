<resources>
    <string name="label_share_app">Share app</string>
    <string name="txtid_writing">Writing</string>
    <string name="txtid_light">Light</string>
    <string name="txtid_dark">Dark</string>
    <string name="txtid_system">System</string>
    <string name="chat_bot">Chat <PERSON>t</string>
    <string name="picked_image">Picked image</string>
    <string name="add_photo">Add Photo</string>
    <string name="type_something">Type something</string>
    <string name="send_prompt">Send prompt</string>
    <string name="txtid_translate">Translate</string>
    <string name="txtid_math">Math</string>
    <string name="math_question">Math Question</string>
    <string name="get_help_with">Get Help With</string>
    <string name="geography">Geography</string>
    <string name="chemistry">Chemistry</string>
    <string name="the_account_has_been_deleted">The account has been deleted</string>
    <string name="account_deletion_failed">Account deletion failed</string>
    <string name="confirm_account_deletion_title">Confirm account deletion</string>
    <string name="confirm_account_deletion_warning">Are you sure you want to delete your account?</string>
    <string name="txtid_delete">Delete</string>
    <string name="txtid_cancel">Cancel</string>
    <string name="txtid_back">Back</string>
    <string name="txtid_done">Done</string>
    <string name="rotate_image">Rotate Image</string>
    <string name="coming_soon">Coming soon</string>
    <string name="arrow_down">Arrow Down</string>
    <string name="solve_now_using_ai">Solve now using AI</string>
    <string name="button_ai_photo">Button AI photo</string>
    <string name="txtid_retry">Retry</string>
    <string name="sign_in_successful">Sign in successful</string>
    <string name="sign_up">Sign up</string>
    <string name="txtid_login">Login</string>
    <string name="txtid_person">Person</string>
    <string name="txtid_email">Email</string>
    <string name="enter_email">Enter email</string>
    <string name="password">Password</string>
    <string name="enter_password">Enter password</string>
    <string name="confirm_password">Confirm password</string>
    <string name="re_enter_password">Re-enter password</string>
    <string name="email_cannot_be_empty">Email cannot be empty</string>
    <string name="password_cannot_be_empty">Password cannot be empty</string>
    <string name="passwords_do_not_match">Passwords do not match</string>
    <string name="sign_in">Sign In</string>
    <string name="txtid_successful">Successful</string>
    <string name="txtid_failed">Failed:</string>
    <string name="already_have_an_account">Already have an account?</string>
    <string name="don_t_have_an_account">Don\'t have an account?</string>
    <string name="login_with_google">Login with Google</string>
    <string name="txtid_ai_chat">AI Chat</string>
    <string name="txtid_loading">Loading…</string>
    <string name="txtid_history">History</string>
    <string name="search_history">Search history…</string>
    <string name="txtid_date">Date</string>
    <string name="txtid_name">Name</string>
    <string name="txtid_favorites">Favorites</string>
    <string name="select_all">Select all</string>
    <string name="not_favorites">Not Favorites</string>
    <string name="txtid_today">Today</string>
    <string name="no_history_available">No history available</string>
    <string name="txtid_edit">Edit</string>
    <string name="edit_history_title">Edit History Title</string>
    <string name="txtid_title">Title</string>
    <string name="txtid_save">Save</string>
    <string name="delete_history">Delete History</string>
    <string name="are_you_sure_you_want_to_delete">Are you sure you want to delete</string>
    <string name="camera_access">Camera access</string>
    <string name="camera_access_message">We need to access the camera to scan your question. You can edit it anytime in Settings.</string>
    <string name="txtid_allow">Allow</string>
    <string name="txtid_deny">Deny</string>
    <string name="flash_is_not_supported_in_front_camera">Flash is not supported in the front camera!</string>
    <string name="flash_toggled">Flash toggled!</string>
    <string name="camera_permission_is_required">Camera permission is required to use this feature.</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="setting">Settings</string>
    <string name="new_feature">New Feature</string>
    <string name="subscribe_for_exclusive_benefit">Subscribe for exclusive benefits</string>
    <string name="subscribe_now">Subscribe now</string>
    <string name="txtid_settings">Settings</string>
    <string name="select_ai_model">Select AI Model</string>
    <string name="confirm_exit">Confirm exit</string>
    <string name="are_you_sure_you_want_to_exit_the_app">Are you sure you want to exit the app?</string>
    <string name="txtid_no">No</string>
    <string name="learn_math_the_easy_way">Learn Math the Easy Way</string>
    <string name="accept_terms_here">"By accessing or using EZ Math and its Services, you confirm that you understand and agree to our %1$s here."</string>
    <string name="terms_and_services">Terms and Services</string>
    <string name="free_message_remain">You have %1$d free messages left.</string>
    <string name="query_text_with_gemini">Query text with Gemini</string>
    <string name="query_text_with_gpt">Query text with Chat GPT</string>
    <string name="query_image_with_gemini">Query image with Gemini</string>
    <string name="query_image_with_gpt">Query image with Chat GPT</string>
    <string name="get_title">Get title</string>
    <string name="selected">Selected</string>
    <string name="txtid_theme" context="App Theme Color">Themes</string>
    <string name="ai_assistance">AI Assistance</string>
    <string name="remove_ads">Remove ads</string>
    <string name="txtid_share">Share</string>
    <string name="txtid_feedback">Feedback</string>
    <string name="txtid_rate_app">Rate app</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="txtid_scan">Scan</string>
    <string name="account_information">Account Information</string>
    <string name="logged_out_successfully">Logged out successfully</string>
    <string name="log_out">Log out</string>
    <string name="delete_account">Delete Account</string>
    <string name="crop_question_inside_frame">Crop just one question inside the frame</string>
    <string name="translate_to">Translate to</string>
    <string name="txtid_confirm">Confirm</string>
    <string name="txtid_ok">OK</string>
    <string name="restore_subscription">Restore subscription</string>
    <string name="txtid_ai_chat_plus">AI Chat Plus</string>
    <string name="access_advanced_features">Access our most powerful model and advanced features</string>
    <string name="save_history">Save History</string>
    <string name="mark_content">Mark content</string>
    <string name="use_anytime">Use anytime</string>

    <string name="new_stres_restore_pro_success">Pro features have been restored successfully</string>
    <string name="new_stres_restore_pro_failed">Restore purchase failed, please try again later</string>
    <string name="errors_please_try_again">An error occurred, please try again</string>

    <string name="txtid_default">Default</string>
    <string name="txtid_you">You</string>
    <string name="txtid_version">Version %1$s</string>
    <string name="dialog_need_account_title">Sign In Required</string>
    <string name="dialog_need_account_body">In order to use this feature, you need to sign in first</string>
    <string name="log_in_again">Log in again</string>
    <string name="log_in_to_delete_account">Please log in again to delete your account</string>
    <string name="login_successful">Login successful</string>
    <string name="purchase_successful">Purchase successful</string>
    <string name="purchase_failed">Purchase failed</string>
    <string name="txtid_calculator">Calculator</string>
    <string name="txtid_close">Close</string>
    <string name="txtid_choose_a_plan">Choose a plan:</string>
    <string name="txtid_never_lose_important_chat">Never lose important chats.</string>
    <string name="txtid_highlight_info">Highlight and revisit key information.</string>
    <string name="txtid_24_7_access">Enjoy 24/7 access.</string>
    <string name="txtid_no_hidden_fees">No hidden fees</string>
    <string name="txtid_most_popular">Most popular</string>
    <string name="txtid_free">Free</string>
    <string name="txtid_no_title">No title</string>
    <string name="enter_your_message_here">Enter your message here…</string>
    <string name="write_a_message">Write a message…</string>

    <string name="txtid_math_subtitle">Solve math problems easily</string>
    <string name="txtid_writing_subtitle">Writing help and guidance</string>
    <string name="chemistry_subtitle">Understand chemical reactions</string>
    <string name="geography_subtitle">World knowledge</string>
    <string name="txtid_translate_subtitle">Multiple languages</string>

    <string name="txtid_or">Or:</string>
    <string name="reward_ad_not_ready">Reward ad is not ready yet</string>

    <string name="txtid_insufficient_credits">Insufficient Credits</string>
    <string name="txtid_get_credits">Get Credits</string>
    <string name="txtid_watch_ad">Watch Ad</string>
    <string name="txtid_watch_reward_ad">Watch reward ad</string>
    <string name="go">Go</string>
    <string name="feedback_and_suggestion">Feedback and Suggestion</string>
    <string name="developer">Developer</string>
    <string name="account">Account</string>
    <string name="select_your_theme">Select your theme</string>
    <string name="chat_gpt">Chat GPT</string>
    <string name="smart_fast_creative">Smart, fast, creative</string>
    <string name="germini">Germini</string>
    <string name="deep_versatile_powerful">Deep, versatile, powerful</string>
    <string name="other_subjects">other subjects</string>
    <string name="try_now">Try now</string>
    <string name="feature_card_literature">Literature</string>
    <string name="feature_card_write_an_essay">Write an essay</string>
    <string name="feature_card_analysis_of_literary_works_stories_poems_plays">Analysis of literary works\n(stories, poems, plays)</string>
    <string name="feature_card_let_ai_assist_you_from_start_to_finish">Let AI assist you from start to finish!</string>
    <string name="feature_card_research_and_analysis">Research and analysis</string>
    <string name="feature_card_find_evalute_interpret_and_visualize_information">Find, evalute, interpret and visualize information</string>
    <string name="hello">Hello</string>
    <string name="how_can_i_assist_you_today">How can I assist you today?</string>
    <string name="chat_history">Chat history</string>
    <string name="ask_anything_get_yout_answer">Ask anything, get yout answer</string>
    <string name="copy">Copy</string>
    <string name="deleting_your_account_will_erase_all_chat_history_personal_settings_and_related_data_this_action_cannot_be_undone">Deleting your account will erase all chat history, personal settings, and related data.\nThis action cannot be undone</string>
    <string name="sure_you_want_to_delete_account">Sure you want to delete account?</string>
    <string name="top_up_coins">Top-up coins</string>
    <string name="used_coins">Used coins</string>
    <string name="days">Days</string>
    <string name="crop_only_one_question_to_get_better_answer">Crop only one question to get better answer</string>
    <string name="recent_languages">Recent Languages</string>
    <string name="all_languages">All Languages</string>
    <string name="search_languages">Search Languages</string>
    <string name="ok_got_it">OK, Got It!</string>
    <string name="tap_here_to_pick_the_one_that_best_suits_your_task">Tap here to pick the one that best suits your task</string>
    <string name="multiple_ai_models_available">Multiple AI models available!</string>
    <string name="search">Search</string>
    <string name="unselect_all">Unselect all</string>
    <string name="select_language">Select language</string>
    <string name="are_you_sure_you_want_to_log_out">Are you sure you want to log out?</string>
    <string name="no_conversations_here_yet">No conversations here yet!</string>
    <string name="try_asking_a_question_to_get_started">Try asking a question to get started!</string>
    <string name="paragraph">Paragraph</string>
    <string name="write_a_short_paragraph_about_the_importance_of_sleep">Write a short paragraph about the importance of sleep.</string>
    <string name="email">Email</string>
    <string name="help_me_write_a_professional_leave_request_email">Help me write a professional leave request email.</string>
    <string name="intro">Intro</string>
    <string name="write_an_introduction_paragraph_for_an_essay_on_climate_change">Write an introduction paragraph for an essay on climate change.</string>
    <string name="equation">Equation</string>
    <string name="help_me_solve_this_equation_2x_3_11">Help me solve this equation: 2x + 3 = 11</string>
    <string name="derivative">Derivative</string>
    <string name="show_me_how_to_find_the_derivative_of_f_x_x_sin_x">Show me how to find the derivative of f(x) = x² · sin(x)</string>
    <string name="limit">Limit</string>
    <string name="find_the_limit_of_sin_x_x_as_x_approaches_0">Find the limit of sin(x)/x as x approaches 0</string>
    <string name="english">English</string>
    <string name="translate_this_sentence_into_english_t_i_r_t_th_ch_h_c_ngo_i_ng">Translate this sentence into English: \'Tôi rất thích học ngoại ngữ.\'</string>
    <string name="meaning">Meaning</string>
    <string name="what_does_the_word_serendipity_mean_in_vietnamese">What does the word \'serendipity\' mean in Vietnamese?</string>
    <string name="japanese">Japanese</string>
    <string name="help_me_translate_this_paragraph_into_japanese">Help me translate this paragraph into Japanese.</string>
    <string name="neutralize">Neutralize</string>
    <string name="explain_what_an_acid_base_neutralization_reaction_is">Explain what an acid-base neutralization reaction is.</string>
    <string name="ethanol">Ethanol</string>
    <string name="what_is_the_structural_formula_of_ethanol">What is the structural formula of ethanol?</string>
    <string name="bonds">Bonds</string>
    <string name="what_is_the_difference_between_ionic_and_covalent_bonds">What is the difference between ionic and covalent bonds?</string>
    <string name="capital">Capital</string>
    <string name="what_is_the_capital_of_brazil_what_s_special_about_it">What is the capital of Brazil? What\'s special about it?</string>
    <string name="compare">Compare</string>
    <string name="compare_the_geography_of_asia_and_africa">Compare the geography of Asia and Africa.</string>
    <string name="desert">Desert</string>
    <string name="why_is_the_sahara_the_largest_desert_in_the_world">Why is the Sahara the largest desert in the world?</string>
    <string name="start">Start</string>
    <string name="hi_what_can_you_help_me_with_today">Hi! What can you help me with today?</string>
    <string name="explain">Explain</string>
    <string name="can_you_explain_how_this_app_works">Can you explain how this app works?</string>
    <string name="tips">Tips</string>
    <string name="give_me_tips_for_getting_the_best_results">Give me tips for getting the best results.</string>
    <string name="exit_app">Exit App</string>
    <string name="do_you_want_to_leave_now_don_t_forget_to_come_back_i_ll_be_waiting_for_you">Do you want to leave now? Don\'t forget to come back, I\'ll be waiting for you!</string>

    <string name="gemini">Gemini</string>

    <!-- ======================= TOPIC SUGGESTIONS ======================= -->
    <string name="describe_place">A Peaceful Place</string>
    <string name="describe_a_place_that_makes_you_feel_at_peace">Describe a place that makes you feel at peace</string>

    <string name="favorite_memory">Favorite Childhood Memory</string>
    <string name="describe_your_favorite_childhood_memory">Describe your favorite childhood memory</string>

    <string name="inspiring_event">Life-Changing Event</string>
    <string name="describe_an_event_that_changed_your_life">Describe an event that changed your life</string>

    <!-- ======================= ESSAY TYPES ======================= -->
    <string name="argumentative">Argumentative Essay</string>
    <string name="argumentative_essay_example">Write an argumentative essay about school uniforms</string>

    <string name="narrative">Narrative Essay</string>
    <string name="narrative_essay_example">Write a narrative essay about overcoming a challenge</string>

    <string name="expository">Expository Essay</string>
    <string name="expository_essay_example">Write an expository essay explaining the water cycle</string>

    <!-- ======================= WORD COUNT ======================= -->
    <string name="word_300">300 Words</string>
    <string name="write_essay_300_words">Write a 300-word essay on this topic</string>

    <string name="word_500">500 Words</string>
    <string name="write_essay_500_words">Write a 500-word essay on this topic</string>

    <string name="word_1000">1000 Words</string>
    <string name="write_essay_1000_words">Write a 1000-word essay on this topic</string>

    <!-- ======================= TONE & STYLE ======================= -->
    <string name="formal">Formal Tone</string>
    <string name="write_formally_about_topic">Write formally about this topic</string>

    <string name="academic">Academic Tone</string>
    <string name="write_academically_about_topic">Write in an academic tone for this topic</string>

    <string name="informal">Informal / Blog Style</string>
    <string name="write_informally_like_a_blog_post">Write informally like a blog post</string>

    <!-- ======================= BOOK TITLES ======================= -->
    <string name="the_great_gatsby">The Great Gatsby</string>
    <string name="write_about_the_great_gatsby">Write about "The Great Gatsby"</string>

    <string name="to_kill_a_mockingbird">To Kill a Mockingbird</string>
    <string name="write_about_to_kill_a_mockingbird">Write about "To Kill a Mockingbird"</string>

    <string name="romeo_and_juliet">Romeo and Juliet</string>
    <string name="write_about_romeo_and_juliet">Write about "Romeo and Juliet"</string>

    <!-- ======================= AUTHORS ======================= -->
    <string name="f_scott_fitzgerald">F. Scott Fitzgerald</string>
    <string name="write_about_f_scott_fitzgerald">Write about the author F. Scott Fitzgerald</string>

    <string name="harper_lee">Harper Lee</string>
    <string name="write_about_harper_lee">Write about the author Harper Lee</string>

    <string name="william_shakespeare">William Shakespeare</string>
    <string name="write_about_william_shakespeare">Write about the author William Shakespeare</string>

    <!-- ======================= LITERARY ANALYSIS ======================= -->
    <string name="character_analysis">Character Analysis</string>
    <string name="do_character_analysis">Do a character analysis of the main character</string>

    <string name="main_themes">Main Themes</string>
    <string name="analyze_main_themes">Analyze the main themes in the story</string>

    <string name="symbolism">Symbolism</string>
    <string name="explain_the_symbolism_in_the_work">Explain the symbolism used in the work</string>

    <!-- ======================= RESPONSE LENGTH ======================= -->
    <string name="length_300">300-word Essay</string>
    <string name="write_300_words_on_topic">Write a 300-word response on the topic</string>

    <string name="length_500">500-word Essay</string>
    <string name="write_500_words_on_topic">Write a 500-word response on the topic</string>

    <string name="length_1000">1000-word Essay</string>
    <string name="write_1000_words_on_topic">Write a 1000-word response on the topic</string>

    <!-- ======================= ACADEMIC LEVEL (ESSAY) ======================= -->
    <string name="middle_school">Middle School</string>
    <string name="write_for_middle_school">Write suitable for middle school students</string>

    <string name="high_school">High School</string>
    <string name="write_for_high_school">Write suitable for high school students</string>

    <string name="university">University</string>
    <string name="write_for_university">Write suitable for university students</string>

    <!-- ======================= RESEARCH TOPICS ======================= -->
    <string name="climate_change">Climate Change</string>
    <string name="research_on_climate_change">Research on climate change and its impact</string>

    <string name="ai_on_jobs">AI and Jobs</string>
    <string name="research_on_ai_and_jobs">Research on the impact of AI on jobs</string>

    <string name="social_media">Social Media and Youth</string>
    <string name="research_on_social_media_effects">Research on the effects of social media on youth</string>

    <!-- ======================= RESEARCH GOALS ======================= -->
    <string name="info_gathering">Information Gathering</string>
    <string name="goal_information_gathering">Goal: Gather general information on the topic</string>

    <string name="trend_analysis">Trend Analysis</string>
    <string name="goal_trend_analysis">Goal: Analyze trends over the last decade</string>

    <string name="problem_solution">Problem &amp; Solution</string>
    <string name="goal_problem_and_solution">Goal: Identify a problem and propose a solution</string>

    <!-- ======================= RESEARCH SOURCES ======================= -->
    <string name="scientific_journals">Scientific Journals</string>
    <string name="use_scientific_journals">Use scientific journals as main sources</string>

    <string name="official_articles">Official Articles</string>
    <string name="use_official_articles">Use official articles and reports as references</string>

    <string name="books">Books</string>
    <string name="use_books_as_references">Use books as the main source of information</string>

    <!-- ======================= ACADEMIC LEVEL (RESEARCH) ======================= -->
    <string name="high_school_students">High School Students</string>
    <string name="research_for_high_school">Make it suitable for high school-level research</string>

    <string name="college_students">College Students</string>
    <string name="research_for_college_students">Make it suitable for college-level research</string>

    <string name="advanced_research">Advanced Research</string>
    <string name="research_for_advanced_level">Make it suitable for advanced academic research</string>


    <!-- Onboarding Strings -->
    <string name="onboarding_title_1">EZ Math</string>
    <string name="onboarding_desc_1">AI app that solves Math problems quickly, accurately, and clearly, making tough questions easy.</string>
    <string name="onboarding_title_2">Snap &amp; Solve</string>
    <string name="onboarding_desc_2">Homework stress? Not anymore. Snap a pic, get the answer, and learn smarter everyday!</string>
    <string name="onboarding_title_3">Multi-AI Response</string>
    <string name="onboarding_desc_3">Every question opens a door — and multiple AIs offer unique answers to guide your way.</string>
    <string name="skip">skip</string>
    <string name="next">next</string>
    <string name="get_started">get started</string>
    <string name="copied_text">Copied Text</string>
    <string name="copied_to_clipboard">Copied to clipboard</string>
    <string name="nothing_to_copy">Nothing to copy</string>
    <string name="content_splash">Powered by Ai. Driven by you</string>
    <string name="required_fields_message">Please enter required fields: %1$s</string>
    <string name="ok">OK</string>
    <string name="watch_ad_to_get_reward">Watch Ad to Get Reward</string>
    <string name="txtid_never_lose_important_chats">Never lose important chats</string>
    <string name="txtid_ask_unlimited_get_ultimate_support">Ask unlimited, get ultimate support</string>
    <string name="multi_ai_response">Multi-AI Response</string>
    <string name="saved_history_sync_across_platforms">Saved history sync across platforms</string>
    <string name="or">or</string>
    <string name="watch_an_ad">watch an ad</string>
    <string name="unlock_chat_access_instanly">Unlock Chat Access Instanly</string>
    <string name="quick_easy_no_payment_needed">Quick &amp; easy - no payment needed</string>
    <string name="you_ve_used_up_all_your_credits_let_s_get_you_chatting_again">You’ve used up all your credits. Let’s get you chatting again!</string>
    <string name="out_of_credits">Out of Credits</string>
    <string name="buy_credits_today_starting_from_just_limited_time_deal">Buy Credits today, starting from just %1$s! Limited-time deal!</string>
</resources>