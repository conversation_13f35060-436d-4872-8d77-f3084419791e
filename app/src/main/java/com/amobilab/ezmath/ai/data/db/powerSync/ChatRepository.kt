package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.utils.debugLog
import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.Queries
import com.powersync.db.SqlCursor
import com.powersync.db.getBytesOptional
import com.powersync.db.getLong
import com.powersync.db.getLongOptional
import com.powersync.db.getString
import com.powersync.db.getStringOptional
import com.powersync.db.internal.PowerSyncTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.Base64
import java.util.UUID

data class ChatEntityPowerSync(
    val id: String = "",
    val historyId: String,
    val timestamp: Long,
    val content: String,
    val isHuman: Boolean,
    val isError: <PERSON><PERSON><PERSON>,
    val imageData: ByteArray?,
    val botName: String?,
    val isLike: Int? = null // null: ch<PERSON>a <PERSON> gi<PERSON>, 1: like, 0: dislike
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ChatEntityPowerSync

        if (timestamp != other.timestamp) return false
        if (isHuman != other.isHuman) return false
        if (isError != other.isError) return false
        if (id != other.id) return false
        if (historyId != other.historyId) return false
        if (content != other.content) return false
        if (!imageData.contentEquals(other.imageData)) return false
        if (botName != other.botName) return false
        if (isLike != other.isLike) return false

        return true
    }

    override fun hashCode(): Int {
        var result = timestamp.hashCode()
        result = 31 * result + isHuman.hashCode()
        result = 31 * result + isError.hashCode()
        result = 31 * result + id.hashCode()
        result = 31 * result + historyId.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + (imageData?.contentHashCode() ?: 0)
        result = 31 * result + (botName?.hashCode() ?: 0)
        result = 31 * result + (isLike?.hashCode() ?: 0)
        return result
    }
}

class ChatRepository(private val database: PowerSyncDatabase) {

    // Mapper sử dụng các phương thức tiện ích của SqlCursor
    private val chatEntityMapper: (SqlCursor) -> ChatEntityPowerSync = { cursor ->
        val columnMap = cursor.columnNames
        ChatEntityPowerSync(
            id = cursor.getString(columnMap["id"] ?: 0) ?: "",
            historyId = cursor.getString(columnMap["history_id"] ?: 0) ?: "",
            timestamp = cursor.getLong(columnMap["timestamp"] ?: 0) ?: 0L,
            content = cursor.getStringOptional("content") ?: "",
            isHuman = cursor.getBoolean(columnMap["is_human"] ?: 0) ?: false,
            isError = cursor.getBoolean(columnMap["is_error"] ?: 0) ?: false,
            imageData = decodeFromBase64(cursor.getString(columnMap["image_data"]?:0) ?: ""),
            botName = cursor.getStringOptional("bot_name"),
            isLike = cursor.getLongOptional("is_like")?.toInt()
        )
    }

    // Lấy danh sách Chat theo historyId
    suspend fun getChatsForHistory(historyId: String): List<ChatEntityPowerSync> = withContext(Dispatchers.IO) {
        debugLog("aab historyIdaa $historyId" )
        try {
            database.getAll(
                sql = "SELECT * FROM chat_table WHERE history_id = ? ORDER BY timestamp ASC",
                parameters = listOf(historyId),
                mapper = chatEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy danh sách ChatEntity: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Chèn ChatEntity
    suspend fun insertChat(chat: ChatEntityPowerSync): String = withContext(Dispatchers.IO) {
        debugLog("aab insertChat ${chat.historyId}" )
        database.writeTransaction { transaction ->
            try {
                val id = UUID.randomUUID().toString()

                transaction.execute(
                    sql = """
                        INSERT OR REPLACE INTO chat_table
                        (id, history_id, timestamp, content, is_human, is_error, image_data, bot_name, is_like)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """.trimIndent(),
                    parameters = listOf(
                        id,
                        chat.historyId,
                        chat.timestamp,
                        chat.content,
                        if (chat.isHuman) 1 else 0,
                        if (chat.isError) 1 else 0,
                        chat.imageData?.let {
                            debugLog("aab byteArr ${it}")
                            encodeToBase64(it)
                                            },
                        chat.botName,
                        chat.isLike
                    )
                )
                id
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi chèn ChatEntity: ${e.message}", e)
            }
        }
    }

    // Cập nhật ChatEntity
    suspend fun updateChat(chat: ChatEntityPowerSync) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE chat_table
                        SET history_id = ?, timestamp = ?, content = ?, is_human = ?, is_error = ?, image_data = ?, bot_name = ?, is_like = ?
                        WHERE id = ?
                    """.trimIndent(),
                    parameters = listOf(
                        chat.historyId,
                        chat.timestamp,
                        chat.content,
                        if (chat.isHuman) 1 else 0,
                        if (chat.isError) 1 else 0,
                        chat.imageData?.let { encodeToBase64(it) },
                        chat.botName,
                        chat.isLike,
                        chat.id
                    )
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật ChatEntity: ${e.message}", e)
            }
        }
    }

    // Cập nhật is_like cho chat
    suspend fun updateChatLike(chatId: String, isLike: Int?) = withContext(Dispatchers.IO) {
        debugLog( "aab updateChatLike chatId: $chatId, isLike: $isLike")
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE chat_table
                        SET is_like = ?
                        WHERE id = ?
                    """.trimIndent(),
                    parameters = listOf(isLike, chatId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật is_like: ${e.message}", e)
            }
        }
    }

    // Xóa các Chat theo historyId
    suspend fun deleteChatsForHistory(historyId: String) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = "DELETE FROM chat_table WHERE history_id = ?",
                    parameters = listOf(historyId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa ChatEntity theo historyId: ${e.message}", e)
            }
        }
    }

    // Xóa các Chat theo danh sách historyIds
    suspend fun deleteChatsForHistories(historyIds: List<String>) = withContext(Dispatchers.IO) {
        if (historyIds.isEmpty()) return@withContext
        database.writeTransaction { transaction ->
            try {
                val placeholders = historyIds.joinToString(",") { "?" }
                transaction.execute(
                    sql = "DELETE FROM chat_table WHERE history_id IN ($placeholders)",
                    parameters = historyIds
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa nhiều ChatEntity: ${e.message}", e)
            }
        }
    }

    // Lấy tin nhắn cuối cùng theo historyId
    suspend fun getLastChat(historyId: String): ChatEntityPowerSync? = withContext(Dispatchers.IO) {
        try {
            database.getOptional(
                sql = "SELECT * FROM chat_table WHERE history_id = ? ORDER BY id DESC LIMIT 1",
                parameters = listOf(historyId),
                mapper = chatEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy tin nhắn cuối cùng: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Theo dõi các Chat theo historyId theo thời gian thực
    fun watchChatsForHistory(historyId: String): Flow<List<ChatEntityPowerSync>> {
        return database.watch(
            sql = "SELECT * FROM chat_table WHERE history_id = ?",
            parameters = listOf(historyId),
            mapper = chatEntityMapper
        )
    }

    // Hàm tiện ích: Mã hóa/giải mã ByteArray
    private fun encodeToBase64(bytes: ByteArray): String =
        Base64.getEncoder().encodeToString(bytes)

    private fun decodeFromBase64(str: String): ByteArray =
        Base64.getDecoder().decode(str)
}