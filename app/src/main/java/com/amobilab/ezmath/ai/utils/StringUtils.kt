package com.amobilab.ezmath.ai.utils

import java.util.Locale

object StringUtils {
    /**
     * Removes diacritics (accents) from Vietnamese text
     * @param text The text to normalize
     * @return Text with diacritics removed
     */
    fun normalizeVietnamese(text: String): String {
        return text
            .replace("[àáạảãâầấậẩẫăằắặẳẵ]".toRegex(), "a")
            .replace("[èéẹẻẽêềếệểễ]".toRegex(), "e")
            .replace("[ìíịỉĩ]".toRegex(), "i")
            .replace("[òóọỏõôồốộổỗơờớợởỡ]".toRegex(), "o")
            .replace("[ùúụủũưừứựửữ]".toRegex(), "u")
            .replace("[ỳýỵỷỹ]".toRegex(), "y")
            .replace("[đ]".toRegex(), "d")
            .replace("[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]".toRegex(), "A")
            .replace("[ÈÉẸẺẼÊỀẾỆỂỄ]".toRegex(), "E")
            .replace("[ÌÍỊỈĨ]".toRegex(), "I")
            .replace("[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]".toRegex(), "O")
            .replace("[ÙÚỤỦŨƯỪỨỰỬỮ]".toRegex(), "U")
            .replace("[ỲÝỴỶỸ]".toRegex(), "Y")
            .replace("[Đ]".toRegex(), "D")
    }

    fun formatCoin(coinTotal: Long): String {
        return when {
            coinTotal > 1_000_000 -> {
                val value = coinTotal.toDouble() / 1_000_000
                if (value % 1 == 0.0) "${value.toLong()}M"
                else String.format(Locale.getDefault(), "%,.1fM", value)
            }
            coinTotal > 10_000 -> {
                val value = coinTotal.toDouble() / 1_000
                if (value % 1 == 0.0) "${value.toLong()}K"
                else String.format(Locale.getDefault(), "%,.1fK", value)
            }
            else -> String.format(Locale.getDefault(), "%,d", coinTotal)
        }
    }
}