package com.amobilab.ezmath.ai.presentation.ui.feature_screen

import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import android.content.Context
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext

import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.SuggestFeature
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureField
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.base.FeatureScreenBase

@AppPreview
@Composable
fun LiteratureScreenPreview() {
    LiteratureScreen(
        onSend = { mode: ChatQuestionMode, prompt: String, imageUri: String ->
            debugLog("onSend: $mode, $prompt, $imageUri")
        }
    )
}

@Composable
fun LiteratureScreen(
    onSend: (mode: ChatQuestionMode, prompt: String, imageUri: String) -> Unit,
) {
    val context = LocalContext.current
    // Define fields for the literature screen
    val fields = listOf(
        FeatureField(
            id = "title",
            label = stringResource(R.string.label_title_of_work),
            suggestions = SuggestFeature.TitleOfWork.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "author",
            label = stringResource(R.string.label_author),
            suggestions = SuggestFeature.Author.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "analysis",
            label = stringResource(R.string.label_analysis_type),
            suggestions = SuggestFeature.AnalysisType.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "format",
            label = stringResource(R.string.label_format),
            suggestions = SuggestFeature.LengthFormat.listSuggestions,
            value = ""
        ),
        FeatureField(
            id = "academicLevel",
            label = stringResource(R.string.label_academic_level),
            suggestions = SuggestFeature.AcademicLevelEssay.listSuggestions,
            value = ""
        )
    )

    // Use the base component with custom content above fields
    FeatureScreenBase(
        title = stringResource(R.string.feature_card_literature),
        fields = fields,
        generateButtonText = stringResource(R.string.generate_outline),
        generateOutline = { fieldMap ->
            generateLiteratureOutline(
                context = context,
                title = fieldMap["title"] ?: "",
                author = fieldMap["author"] ?: "",
                analysisType = fieldMap["analysis"] ?: "",
                format = fieldMap["format"] ?: "",
                academicLevel = fieldMap["academicLevel"] ?: ""
            )
        },
        onSend = { prompt, imageUri ->
            onSend(ChatQuestionMode.Literature, prompt, imageUri)
        },
        contentAboveFields = {
            AppText(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                text = stringResource(R.string.literature_screen_description),
                color = AppColors.current.text,
                fontSize = AppFontSize.BODY1,
                lineHeight = 24.sp,
                fontWeight = FontWeight.W500
            )
        },
        requiredFields = listOf("title", "analysis") // Thêm title và author là các trường bắt buộc
    )
}

fun generateLiteratureOutline(
    context: Context,
    title: String,
    author: String,
    analysisType: String,
    format: String,
    academicLevel: String
): String {
    val sb = StringBuilder()

    // Thông tin cơ bản
    sb.append("📘 " + context.getString(R.string.literature_outline_title_label, title)).append("\n")
    sb.append("✍️ " + context.getString(R.string.literature_outline_author_label, author)).append("\n")
    sb.append("🔍 " + context.getString(R.string.literature_outline_focus_label, analysisType)).append("\n")
    sb.append("📏 " + context.getString(R.string.literature_outline_length_label, format)).append("\n")
    sb.append("🎓 " + context.getString(R.string.literature_outline_academic_level_label, academicLevel)).append("\n\n")

    // Tiêu đề outline
    sb.append("🧾 " + context.getString(R.string.literature_outline_outline_label)).append("\n\n")

    // Phần Introduction
    sb.append(context.getString(R.string.literature_outline_introduction_title)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_introduction_context)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_introduction_author)).append("\n\n")

    // Phần Background
    sb.append(context.getString(R.string.literature_outline_background_title)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_background_summary)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_background_context)).append("\n\n")

    // Phần Main Analysis
    sb.append(context.getString(R.string.literature_outline_analysis_title)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_analysis_deep_dive, analysisType)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_analysis_evidence)).append("\n\n")

    // Phần Connections
    sb.append(context.getString(R.string.literature_outline_connections_title)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_connections_themes)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_connections_contrast)).append("\n\n")

    // Phần Conclusion
    sb.append(context.getString(R.string.literature_outline_conclusion_title)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_conclusion_insights)).append("\n")
    sb.append("   - " + context.getString(R.string.literature_outline_conclusion_value)).append("\n")

    return sb.toString()
}