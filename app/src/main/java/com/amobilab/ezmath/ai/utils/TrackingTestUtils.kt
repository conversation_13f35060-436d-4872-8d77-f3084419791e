package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.FirebaseAssist
import android.content.Context
import com.amobilab.ezmath.ai.data.models.Message

/**
 * Utility class để test tracking events
 */
object TrackingTestUtils {
    
    /**
     * Test thumb up tracking event
     */
    fun testThumbUpTracking(context: Context) {
        val aiModel = "Chat GPT"
        val category = "Math"
        val hasImage = "false"
        val countryCode = UnitUtils.getDetectedCountry(context, "US")
        
        FirebaseAssist.instance.logChatThumbUp(
            aiModel = aiModel,
            category = category,
            hasImage = hasImage,
            countryCode = countryCode
        )
        
        println("Thumb up event tracked: aiModel=$aiModel, category=$category, hasImage=$hasImage, countryCode=$countryCode")
    }
    
    /**
     * Test thumb down tracking event
     */
    fun testThumbDownTracking(context: Context) {
        val aiModel = "GEMINI"
        val category = "Writing"
        val hasImage = "true"
        val countryCode = UnitUtils.getDetectedCountry(context, "US")
        
        FirebaseAssist.instance.logChatThumbDown(
            aiModel = aiModel,
            category = category,
            hasImage = hasImage,
            countryCode = countryCode
        )
        
        println("Thumb down event tracked: aiModel=$aiModel, category=$category, hasImage=$hasImage, countryCode=$countryCode")
    }
    
    /**
     * Test home button tracking event
     */
    fun testHomeButtonTracking() {
        val screenName = "HomeScreen_SettingTab"
        FirebaseAssist.instance.logHomeButtonPressed(screenName)
        println("Home button event tracked: screenName=$screenName")
    }
    
    /**
     * Test recent button tracking event
     */
    fun testRecentButtonTracking() {
        val screenName = "CoinHistory"
        FirebaseAssist.instance.logRecentButtonPressed(screenName)
        println("Recent button event tracked: screenName=$screenName")
    }
    
    /**
     * Test country detection
     */
    fun testCountryDetection(context: Context) {
        val countryCode = UnitUtils.getDetectedCountry(context, "US")
        println("Detected country: $countryCode")
    }
    
    /**
     * Test screen tracking
     */
    fun testScreenTracking() {
        // Test HomeScreen with tab
        ScreenTracker.setCurrentScreen("HomeScreen")
        ScreenTracker.setCurrentTab("AiChatTab")
        println("Current screen: ${ScreenTracker.getCurrentScreenForTracking()}")
        
        // Test CoinHistory screen
        ScreenTracker.setCurrentScreen("CoinHistory")
        println("Current screen: ${ScreenTracker.getCurrentScreenForTracking()}")
    }
    
    /**
     * Test tất cả tracking events
     */
    fun testAllTrackingEvents(context: Context) {
        println("=== Testing All Tracking Events ===")
        
        testCountryDetection(context)
        testScreenTracking()
        testThumbUpTracking(context)
        testThumbDownTracking(context)
        testHomeButtonTracking()
        testRecentButtonTracking()
        
        println("=== All Tests Completed ===")
    }
}
