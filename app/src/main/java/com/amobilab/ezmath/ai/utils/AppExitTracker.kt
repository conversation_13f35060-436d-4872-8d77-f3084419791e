package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.dlog
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * Singleton để track app exit events và share state giữa Activity và Compose
 */
object AppExitTracker {
    
    private val _isUserLeavingByHomeButton = MutableStateFlow(false)
    val isUserLeavingByHomeButton: StateFlow<Boolean> = _isUserLeavingByHomeButton
    
    /**
     * Set flag khi user nhấn home button (gọi từ Activity)
     */
    fun setHomeButtonPressed() {
        _isUserLeavingByHomeButton.value = true
        
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logHomeButtonPressed(currentScreen)
        
        dlog("HOME BUTTON PRESSED - Screen: $currentScreen")
    }
    
    /**
     * Log recent button pressed (gọ<PERSON> từ Compose)
     */
    fun logRecentButtonPressed() {
        dlog("logRecentButtonPressed called - isHomeButton: ${_isUserLeavingByHomeButton.value}")
        if (!_isUserLeavingByHomeButton.value) {
            val currentScreen = ScreenTracker.getCurrentScreenForTracking()
            FirebaseAssist.instance.logRecentButtonPressed(currentScreen)

            dlog("RECENT BUTTON PRESSED - Screen: $currentScreen")
        } else {
            dlog("Skipped recent button - was home button")
        }
    }
    
    /**
     * Reset flag khi app resume
     */
    fun resetFlags() {
        _isUserLeavingByHomeButton.value = false
    }
    
    /**
     * Get current flag value
     */
    fun isHomeButtonPressed(): Boolean {
        return _isUserLeavingByHomeButton.value
    }
}
