package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.dlog
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes

/**
 * Test utility để kiểm tra app exit tracking events
 */
object AppExitTrackingTest {
    
    /**
     * Test home button tracking với các screen khác nhau
     */
    fun testHomeButtonTracking() {
        dlog("=== Testing Home Button Tracking ===")
        
        // Test với HomeScreen + AiChatTab
        ScreenTracker.setCurrentScreen("HomeScreen")
        ScreenTracker.setCurrentTab(ScreenRoutes.AiChatTab().route)
        val screen1 = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logHomeButtonPressed(screen1)
        dlog("Test 1 - Home button pressed: $screen1")
        
        // Test với HomeScreen + SettingTab
        ScreenTracker.setCurrentTab(ScreenRoutes.SettingTab().route)
        val screen2 = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logHomeButtonPressed(screen2)
        dlog("Test 2 - Home button pressed: $screen2")
        
        // Test với CoinHistory screen
        ScreenTracker.setCurrentScreen("CoinHistory")
        val screen3 = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logHomeButtonPressed(screen3)
        dlog("Test 3 - Home button pressed: $screen3")
    }
    
    /**
     * Test recent button tracking với các screen khác nhau
     */
    fun testRecentButtonTracking() {
        dlog("=== Testing Recent Button Tracking ===")
        
        // Test với HomeScreen + ScanTab
        ScreenTracker.setCurrentScreen("HomeScreen")
        ScreenTracker.setCurrentTab(ScreenRoutes.ScanTab().route)
        val screen1 = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logRecentButtonPressed(screen1)
        dlog("Test 1 - Recent button pressed: $screen1")
        
        // Test với HomeScreen + HistoryTab
        ScreenTracker.setCurrentTab(ScreenRoutes.HistoryTab().route)
        val screen2 = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.instance.logRecentButtonPressed(screen2)
        dlog("Test 2 - Recent button pressed: $screen2")
    }
    
    /**
     * Chạy tất cả tests
     */
    fun runAllTests() {
        dlog("========================================")
        dlog("Starting App Exit Tracking Tests")
        dlog("========================================")
        
        testHomeButtonTracking()
        dlog("")
        
        testRecentButtonTracking()
        
        dlog("========================================")
        dlog("App Exit Tracking Tests Completed")
        dlog("========================================")
    }
}
