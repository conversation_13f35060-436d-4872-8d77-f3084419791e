package com.amobilab.ezmath.ai.data.db

import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.data.db.powerSync.MyConnector
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.util.concurrent.TimeUnit

class CoinRepository {
    companion object {
        private const val BACKEND_URL_COIN = "http://167.99.235.209:8088" // Thay thế bằng URL backend thực
        private const val TIMEOUT_SECONDS = 5L
    }

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .build()

    suspend fun getApiCoins(): Long? {
        return withContext(Dispatchers.IO) {
            val userId = FirebaseAuth.getInstance().currentUser?.uid
                ?: return@withContext null

            debugLog("powerSync userId: $userId")

            val request = Request.Builder()
                .url("$BACKEND_URL_COIN/users/by-user-id/$userId")
                .addHeader("Content-Type", "application/json")
                .get()
                .build()

            try {
                val response = httpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    val responseBody = response.body.string()

                    debugLog("powerSync getApiCoins responseBody: $responseBody")
                    val json = JSONObject(responseBody)
                    val coin = json.optJSONObject("data")?.optLong("coin", 0L) ?: 0L
                    debugLog("powerSync coin: $coin")
                    coin
                } else {
                    debugLog("powerSync Lỗi khi lấy coins: ${response.code} - ${response.message}")
                    null
                }
            } catch (e: Exception) {
                debugLog("powerSync Exception: ${e.message}")
                e.printStackTrace()
                null
            }
        }
    }

    suspend fun updateApiCoins(coins: Long): Boolean = withContext(Dispatchers.IO) {
        val user = FirebaseAuth.getInstance().currentUser
        if (user == null) {
            debugLog("powerSync Firebase user không tồn tại")
            return@withContext false
        }

        val firebaseAuthToken = try {
            user.getIdToken(false).await().token ?: ""
        } catch (e: Exception) {
            debugLog("powerSync Lỗi khi lấy Firebase token: ${e.message}")
            return@withContext false
        }

        if (firebaseAuthToken.isEmpty()) {
            debugLog("powerSync Firebase token không hợp lệ hoặc hết hạn")
            return@withContext false
        }

        val userId = user.uid
        val requestData = buildJsonObject {
            put("coin", coins)
        }

        val requestBody = requestData.toString().toRequestBody("application/json".toMediaType())
        val request = Request.Builder()
            .url("$BACKEND_URL_COIN/users/update-coin")
            .put(requestBody)
            .addHeader("User-Id", userId)
            .addHeader("Content-Type", "application/json")
            .addHeader("Firebase-Auth-Token", firebaseAuthToken)
            .build()

        try {
            val response = httpClient.newCall(request).execute()
            if (response.isSuccessful) {
                debugLog("powerSync Cập nhật coins thành công: $coins")
                true
            } else {
                debugLog("powerSync Lỗi khi cập nhật coins: ${response.code} - ${response.message}")
                false
            }
        } catch (e: Exception) {
            debugLog("powerSync Exception khi cập nhật coins: ${e.message}")
            e.printStackTrace()
            false
        }
    }

}