package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.dlog
import android.content.Context
import java.util.Locale

/**
 * Test utility để kiểm tra chat feedback tracking events
 */
object ChatFeedbackTest {
    
    /**
     * Test thumb up tracking event
     */
    fun testThumbUpTracking(context: Context) {
        val aiModel = "Chat GPT"
        val category = "Math"
        val hasImage = "false"
        val countryCode = UnitUtils.getDetectedCountry(context, "US").lowercase(Locale.getDefault())
        
        FirebaseAssist.instance.logChatThumbUp(
            aiModel = aiModel,
            category = category,
            hasImage = hasImage,
            countryCode = countryCode
        )
        
        dlog("THUMB UP TEST - aiModel: $aiModel, category: $category, hasImage: $hasImage, countryCode: $countryCode")
    }
    
    /**
     * Test thumb down tracking event
     */
    fun testThumbDownTracking(context: Context) {
        val aiModel = "GEMINI"
        val category = "Writing"
        val hasImage = "true"
        val countryCode = UnitUtils.getDetectedCountry(context, "US").lowercase(Locale.getDefault())
        
        FirebaseAssist.instance.logChatThumbDown(
            aiModel = aiModel,
            category = category,
            hasImage = hasImage,
            countryCode = countryCode
        )
        
        dlog("THUMB DOWN TEST - aiModel: $aiModel, category: $category, hasImage: $hasImage, countryCode: $countryCode")
    }
    
    /**
     * Test country detection
     */
    fun testCountryDetection(context: Context) {
        val countryCode = UnitUtils.getDetectedCountry(context, "US")
        dlog("Detected country: $countryCode")
    }
    
    /**
     * Test tất cả chat feedback events
     */
    fun testAllChatFeedbackEvents(context: Context) {
        dlog("=== Testing Chat Feedback Events ===")
        
        testCountryDetection(context)
        testThumbUpTracking(context)
        testThumbDownTracking(context)
        
        dlog("=== Chat Feedback Tests Completed ===")
    }
}
