package com.amobilab.ezmath.ai.utils

import android.content.Context
import android.os.Build
import android.telephony.TelephonyManager

fun getDetectedCountry(
    context: Context?,
    defaultCountryIsoCode: String,
): String =
    detectSIMCountry(context)
        ?: detectNetworkCountry(context)
        ?: detectLocaleCountry(context)
        ?: defaultCountryIsoCode

private fun detectSIMCountry(context: Context?): String? {
    try {
        val telephonyManager =
            context!!.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return telephonyManager.simCountryIso
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}

private fun detectNetworkCountry(context: Context?): String? {
    try {
        val telephonyManager =
            context!!.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return telephonyManager.networkCountryIso
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}

private fun detectLocaleCountry(context: Context?): String? {
    try {
        var localeCountryISO: String? = null
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            localeCountryISO =
                context!!
                    .resources.configuration.locales[0]
                    .country
        }
        return localeCountryISO
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}