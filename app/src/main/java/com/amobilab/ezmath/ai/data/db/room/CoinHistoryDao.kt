package com.amobilab.ezmath.ai.data.db.room


import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query

@Dao
interface CoinHistoryDao {

    @Insert
    suspend fun insertTransaction(transaction: CoinHistoryEntity)

    @Query("SELECT * FROM coin_history_table")
    fun getAllTransactions(): List<CoinHistoryEntity>

    @Query("SELECT * FROM coin_history_table ORDER BY date DESC LIMIT 50")
    suspend fun getAllCoinHistory(): List<CoinHistoryEntity>
}