package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.utils.debugLog
import android.annotation.SuppressLint
import android.widget.FrameLayout
import androidx.annotation.DrawableRes
import androidx.annotation.RawRes
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView

@SuppressLint("UnsafeOptInUsageError")
@Composable
fun VideoBackground(
    @RawRes videoResId: Int,
    modifier: Modifier = Modifier,
    @DrawableRes backgroundImageResId: Int? = null,
    backgroundColor: Color = Color.Black
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    var isVideoReady by remember { mutableStateOf(false) }
    var videoAlpha by remember { mutableFloatStateOf(0f) }
    var playerView by remember { mutableStateOf<PlayerView?>(null) }

    val animatedAlpha by animateFloatAsState(
        targetValue = videoAlpha,
        animationSpec = tween(durationMillis = 300),
        label = "videoAlpha"
    )

    val videoUri = remember(videoResId, context) {
        "android.resource://${context.packageName}/$videoResId".toUri()
    }

    val exoPlayer = remember(videoResId, context) {
        ExoPlayer.Builder(context).build()
    }

    val playerListener = remember {
        object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_READY -> {
                        isVideoReady = true
                    }
                    Player.STATE_ENDED -> {
                        // Restart video when it ends
                        exoPlayer.seekTo(0)
                        exoPlayer.play()
                    }
                    Player.STATE_BUFFERING -> {
                    }
                    Player.STATE_IDLE -> {
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                debugLog("Video error: ${error.message}")
                // Show background image when video fails
                videoAlpha = 0f
            }
        }
    }

    DisposableEffect(exoPlayer) {
        exoPlayer.addListener(playerListener)
        onDispose {
            exoPlayer.removeListener(playerListener)
        }
    }

    fun initializePlayer() {
        debugLog("Initializing player with uri: $videoUri")
        val mediaItem = MediaItem.fromUri(videoUri)
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.repeatMode = Player.REPEAT_MODE_ALL
        exoPlayer.videoScalingMode = C.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        exoPlayer.volume = 0f // Tắt âm thanh
        debugLog("Player initialized. playWhenReady=${exoPlayer.playWhenReady}, state=${exoPlayer.playbackState}")
    }

    LaunchedEffect(Unit) {
        initializePlayer()
    }

    LaunchedEffect(playerView) {
        if (playerView != null) {
            playerView?.player = exoPlayer
            exoPlayer.playWhenReady = true
        }
    }

    LaunchedEffect(isVideoReady) {
        if (isVideoReady) {
            videoAlpha = 1f
        }
    }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    playerView?.player = exoPlayer
                    if (exoPlayer.playbackState == Player.STATE_IDLE) {
                        initializePlayer()
                    } else {
                        exoPlayer.playWhenReady = true
                    }
                }

                Lifecycle.Event.ON_PAUSE -> {
                    exoPlayer.pause()
                }

                Lifecycle.Event.ON_STOP -> {
                    exoPlayer.stop()
                    exoPlayer.clearMediaItems()
                }

                else -> Unit
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            debugLog("DisposableEffect: Cleaning up resources")
            lifecycleOwner.lifecycle.removeObserver(observer)
            exoPlayer.release()
            playerView = null
            debugLog("ExoPlayer released")
        }
    }

    Box(modifier = modifier) {
        if (backgroundImageResId != null) {
            Image(
                painter = painterResource(id = backgroundImageResId),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .zIndex(1f),
                contentScale = ContentScale.Crop
            )
        } else {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .zIndex(1f)
                    .background(backgroundColor)
            )
        }

        key(videoResId) {
            AndroidView(
                factory = { ctx ->
                    PlayerView(ctx).apply {
                        useController = false
                        resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                        layoutParams = FrameLayout.LayoutParams(
                            FrameLayout.LayoutParams.MATCH_PARENT,
                            FrameLayout.LayoutParams.MATCH_PARENT
                        )
                        setBackgroundColor(android.graphics.Color.TRANSPARENT)
                        player = exoPlayer
                        playerView = this
                        debugLog("PlayerView created and attached to ExoPlayer")
                    }
                },
                modifier = Modifier
                    .fillMaxSize()
                    .alpha(animatedAlpha)
                    .zIndex(2f) // Đặt video lên trên background
            )
        }
    }
}
