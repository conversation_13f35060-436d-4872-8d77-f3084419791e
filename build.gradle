import org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.jetbrains.kotlin.android) apply false
    alias(libs.plugins.kotlin.parcelize) apply false
    alias(libs.plugins.androidx.navigation.safeargs) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.dagger.hilt.plugin) apply false
    alias(libs.plugins.compose.compiler) apply false
    alias(libs.plugins.firebase.appdistribution) apply false
    alias(libs.plugins.google.services) apply false
    alias(libs.plugins.firebase.crashlytics) apply false
}

allprojects {
    tasks.withType(JavaCompile).configureEach {
        sourceCompatibility = libs.versions.javaVersion.get().toInteger()
        targetCompatibility = libs.versions.javaVersion.get().toInteger()
//        javaCompiler = javaToolchains.compilerFor {
//            languageVersion = JavaLanguageVersion.of(libs.versions.javaVersion.get().toInteger())
//        }
    }

    tasks.withType(KotlinCompile).configureEach {
        kotlinOptions.jvmTarget = libs.versions.javaVersion.get()
    }

    tasks.withType(KaptGenerateStubs).configureEach {
        kotlinOptions.jvmTarget = libs.versions.javaVersion.get()
    }
}

